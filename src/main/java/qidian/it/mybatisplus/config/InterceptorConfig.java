package qidian.it.mybatisplus.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import qidian.it.mybatisplus.interceptor.MyInterceptor;

@Configuration
public class InterceptorConfig implements WebMvcConfigurer {
    @Autowired
    MyInterceptor myInterceptor;

    //注册拦截器
    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        //registry.addInterceptor(myInterceptor).addPathPatterns("/**").excludePathPatterns("/getAllEmp");//拦截所有请求
    }



    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 将上传文件的路径暴露为静态资源
        registry.addResourceHandler("/uploads/**")  // URL前缀
                .addResourceLocations("file:/Users/<USER>/workspace/school/mybatisplus/uploads/");  // 上传文件的实际路径
    }


}
