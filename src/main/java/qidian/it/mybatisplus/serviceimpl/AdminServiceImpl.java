package qidian.it.mybatisplus.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import qidian.it.mybatisplus.config.JwtConfig;
import qidian.it.mybatisplus.entity.Admin;
import qidian.it.mybatisplus.entity.Result;
import qidian.it.mybatisplus.mapper.AdminMapper;
import qidian.it.mybatisplus.service.IAdminService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Service
public class AdminServiceImpl extends ServiceImpl<AdminMapper, Admin> implements IAdminService {
    @Autowired
    JwtConfig jwtConfig;
    @Override
    public Result login(Admin admin) {
     Admin adminSelect=baseMapper.selectOne(new LambdaQueryWrapper<Admin>().eq(Admin::getUsername,admin.getUsername()));

        if(Objects.nonNull(adminSelect)){
            if(Objects.equals(admin.getPassword(),adminSelect.getPassword())){

                return Result.success(jwtConfig.createToken(admin.getUsername()));
            }else{
                return Result.fail("密码错误");
            }
        }
        return Result.fail("用户名不存在");
    }

    @Override
    public Result register(Admin admin) {



        LambdaQueryWrapper<Admin> lambdaQueryWrapper=new LambdaQueryWrapper<>();
        Admin adminSelect=baseMapper.selectOne(lambdaQueryWrapper.eq(Admin::getUsername,admin.getUsername()));
        if(Objects.isNull(adminSelect)){//允许注册
            if(baseMapper.insert(admin)>0){
                return Result.success("注册成功");
            }else{
                return Result.fail("服务繁忙,请稍候");
            }
        }
        return Result.fail("用户名已存在");
    }

}
