package qidian.it.mybatisplus.serviceimpl;

import com.alibaba.excel.EasyExcel;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import qidian.it.mybatisplus.entity.Employee;
import qidian.it.mybatisplus.entity.Result;
import qidian.it.mybatisplus.mapper.EmployeeMapper;
import qidian.it.mybatisplus.service.IEmployeeService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@Service
public class EmployeeServiceImpl extends ServiceImpl<EmployeeMapper, Employee> implements IEmployeeService {

    @Override
    public Result getAllEmp(Integer currentPage) {

        QueryWrapper<Employee> queryWrapper=new QueryWrapper<>();
        IPage<Employee> iPage=new Page<>(currentPage,1);//current为当前页,每页3条数据
        long total=baseMapper.selectPage(iPage,queryWrapper).getTotal();

        return Result.success((int) total,baseMapper.selectPage(iPage,queryWrapper).getRecords());
    }

    @Override
    public Result updateEmp(Employee employee) {

//        LambdaUpdateWrapper<Employee> lambdaUpdateWrapper=new LambdaUpdateWrapper<>();
//       //update employee set age=21,gender='男' where name='hello'
//        lambdaUpdateWrapper.eq(Employee::getName,"hello");
//        baseMapper.update(employee,lambdaUpdateWrapper);
         if(baseMapper.updateById(employee)>0){
             return Result.success("修改成功");
         }
        return Result.fail("服务繁忙");
    }

    @Override
    public void exportData(HttpServletResponse resp) {
      List empList=  baseMapper.selectList(new LambdaQueryWrapper<Employee>());
        System.out.println(empList);
        // 设置文本内省
        resp.setContentType("application/vnd.ms-excel");
        // 设置字符编码
        resp.setCharacterEncoding("utf-8");
        // 设置响应头
        resp.setHeader("Content-disposition", "attachment;filename=demo.xlsx");
        try {
            EasyExcel.write(resp.getOutputStream(), Employee.class).sheet("成员列表").doWrite(empList);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }

}
