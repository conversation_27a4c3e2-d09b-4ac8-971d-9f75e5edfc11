package qidian.it.mybatisplus.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import org.springframework.beans.factory.annotation.Autowired;
import qidian.it.mybatisplus.entity.User;
import qidian.it.mybatisplus.mapper.UserMapper;
import qidian.it.mybatisplus.service.IUserService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements IUserService {
    //会注入UserMapper;

    @Override
    public List<User> selectAllUser() {
        //构造查询全部的SQL
        //select username,password from user whers user_id=2;
        //return baseMapper.selectList(new LambdaQueryWrapper<User>().select(User::getUsername,User::getPassword).eq(User::getUserId,2));

        //分页
        QueryWrapper<User> queryWrapper=new QueryWrapper<>();
        IPage<User> iPage=new Page<>(2,1);//current为当前页,每页3条数据
        return baseMapper.selectPage(iPage,queryWrapper).getRecords();

    }


    @Override
    public User selectByUsername(String username) {
        //select * from user where username=?;
        return baseMapper.selectOne(new LambdaQueryWrapper<User>().eq(User::getUsername,username));
    }


}
