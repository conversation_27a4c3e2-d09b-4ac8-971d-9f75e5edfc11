package qidian.it.mybatisplus.serviceimpl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.web.multipart.MultipartFile;
import qidian.it.mybatisplus.entity.Product;
import qidian.it.mybatisplus.entity.Result;
import qidian.it.mybatisplus.mapper.ProductMapper;
import qidian.it.mybatisplus.service.IProductService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.springframework.stereotype.Service;
import qidian.it.mybatisplus.util.FileUtil;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 *  服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@Service
public class ProductServiceImpl extends ServiceImpl<ProductMapper, Product> implements IProductService {

    @Override
    public Result addProduct(Product product, MultipartFile multipartFile) {

        String url=FileUtil.upload(multipartFile);
        product.setProductImages(url);
        if(baseMapper.insert(product)>0){
            return Result.success("添加成功");
        }
        return Result.fail("服务繁忙");
    }

    @Override
    public void download(HttpServletResponse resp, int productId) {
       String url= baseMapper.selectById(productId).getProductImages();
        FileUtil.download(resp,url);
    }
}
