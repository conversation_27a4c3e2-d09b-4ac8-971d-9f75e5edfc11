package qidian.it.mybatisplus.service;

import qidian.it.mybatisplus.entity.Employee;
import com.baomidou.mybatisplus.extension.service.IService;
import qidian.it.mybatisplus.entity.Result;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 *  服务类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
public interface IEmployeeService extends IService<Employee> {


    Result getAllEmp(Integer currentPage);
    Result updateEmp(Employee employee);
    void exportData(HttpServletResponse resp);

}
