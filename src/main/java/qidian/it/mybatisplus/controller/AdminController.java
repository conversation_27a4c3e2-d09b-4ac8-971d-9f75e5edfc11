package qidian.it.mybatisplus.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import qidian.it.mybatisplus.config.JwtConfig;
import qidian.it.mybatisplus.entity.Admin;
import qidian.it.mybatisplus.entity.Result;
import qidian.it.mybatisplus.serviceimpl.AdminServiceImpl;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-19
 */
@RestController
@CrossOrigin
@RequestMapping("/admin")
public class AdminController {

    @Autowired
    AdminServiceImpl adminService;


    @RequestMapping("/register")
    public Result register(Admin admin){
        return adminService.register(admin);
    }


    @RequestMapping(value = "/invalid",produces = "application/json;charset=utf-8")
    public Result invalid(String username){
        return Result.success("访问成功");
    }


    @RequestMapping("/login")
    public Result login(Admin admin){
    //用户登录成功后生成token
        return adminService.login(admin);
    }








}


