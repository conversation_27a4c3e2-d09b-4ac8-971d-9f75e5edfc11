package qidian.it.mybatisplus.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RestController;
import qidian.it.mybatisplus.entity.User;
import qidian.it.mybatisplus.serviceimpl.UserServiceImpl;

import java.util.List;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-18
 */
@RestController
@RequestMapping("/user")
public class UserController {

    @Autowired
    UserServiceImpl userService;

    @RequestMapping("/selectAll")
    public List<User> selectAll(){
        return  userService.selectAllUser();
    }


    @RequestMapping("/selectByUsername")
    public User selectByUsername(String username) {
        return userService.selectByUsername(username);
    }

    }
