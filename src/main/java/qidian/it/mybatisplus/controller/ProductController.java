package qidian.it.mybatisplus.controller;


import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;

import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import qidian.it.mybatisplus.entity.Product;
import qidian.it.mybatisplus.entity.Result;
import qidian.it.mybatisplus.serviceimpl.ProductServiceImpl;

import javax.servlet.http.HttpServletResponse;

/**
 * <p>
 *  前端控制器
 * </p>
 *
 * <AUTHOR>
 * @since 2025-08-21
 */
@RestController
@CrossOrigin
@RequestMapping("/product")
public class ProductController {

    @Autowired
    ProductServiceImpl productService;
    @RequestMapping("/addProduct")
    public Result addProduct(Product product,  MultipartFile file){
        return productService.addProduct(product,file);
    }

    @RequestMapping("/download")
    public void download(HttpServletResponse resp, int productId) {
        productService.download(resp,productId);
    }

    }
